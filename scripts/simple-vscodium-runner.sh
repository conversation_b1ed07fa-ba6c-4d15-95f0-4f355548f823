#!/usr/bin/env bash

# Simple VSCodium Runner with Dynamic Fingerprint Generation
# Runs VSCodium with different fingerprints on each execution

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"
PROFILES_DIR="/tmp/vscodium-fingerprint-profiles"
VSCODIUM_CMD=""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
Simple VSCodium Fingerprint Runner

USAGE:
    $0 [OPTIONS] [WORKSPACE_PATH]

OPTIONS:
    --new-fingerprint, -n    Generate new fingerprint and run VSCodium
    --fingerprint-id ID      Use specific fingerprint by ID
    --privacy-level LEVEL    Privacy level (low, medium, high, maximum) [default: high]
    --list, -l               List available fingerprints
    --clean, -c              Clean up old profiles and fingerprints
    --help, -h               Show this help

EXAMPLES:
    $0 --new-fingerprint                    # New fingerprint, current directory
    $0 --new-fingerprint /path/to/project   # New fingerprint, specific workspace
    $0 --fingerprint-id fp_20241201_123456  # Use existing fingerprint
    $0 --list                               # List available fingerprints

VSCODIUM INSTALLATION:
    macOS (Homebrew): brew install --cask vscodium
    macOS (Manual):   Download from https://vscodium.com/
    Linux (Snap):     snap install codium --classic
    Linux (Flatpak):  flatpak install com.vscodium.codium

EOF
}

# Parse arguments
GENERATE_NEW=false
FINGERPRINT_ID=""
PRIVACY_LEVEL="high"
LIST_MODE=false
CLEAN_MODE=false
WORKSPACE_PATH="$(pwd)"

while [[ $# -gt 0 ]]; do
    case $1 in
        --new-fingerprint|-n)
            GENERATE_NEW=true
            shift
            ;;
        --fingerprint-id)
            FINGERPRINT_ID="$2"
            shift 2
            ;;
        --privacy-level)
            PRIVACY_LEVEL="$2"
            shift 2
            ;;
        --list|-l)
            LIST_MODE=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            WORKSPACE_PATH="$1"
            shift
            ;;
    esac
done

# Find the correct VSCodium binary
find_vscodium() {
    local vscodium_cmd=""

    # Check common VSCodium locations on macOS
    if [[ -f "/Applications/VSCodium.app/Contents/Resources/app/bin/codium" ]]; then
        vscodium_cmd="/Applications/VSCodium.app/Contents/Resources/app/bin/codium"
    elif [[ -f "/usr/local/bin/codium" ]]; then
        vscodium_cmd="/usr/local/bin/codium"
    elif command -v codium &> /dev/null; then
        vscodium_cmd="codium"
    elif [[ -f "/opt/homebrew/bin/codium" ]]; then
        # Homebrew on Apple Silicon
        vscodium_cmd="/opt/homebrew/bin/codium"
    elif command -v vscodium &> /dev/null; then
        # Alternative command name on some systems
        vscodium_cmd="vscodium"
    elif [[ -f "/snap/bin/codium" ]]; then
        # Snap installation
        vscodium_cmd="/snap/bin/codium"
    elif command -v flatpak &> /dev/null && flatpak list | grep -q "com.vscodium.codium"; then
        # Flatpak installation
        vscodium_cmd="flatpak run com.vscodium.codium"
    fi

    if [[ -z "$vscodium_cmd" ]]; then
        log_error "VSCodium not found!" >&2
        log_info "Please install VSCodium:" >&2
        log_info "  macOS (Homebrew): brew install --cask vscodium" >&2
        log_info "  macOS (Manual):   Download from https://vscodium.com/" >&2
        log_info "  Linux (Snap):     snap install codium --classic" >&2
        log_info "  Linux (Flatpak):  flatpak install com.vscodium.codium" >&2
        exit 1
    fi

    log_info "Using VSCodium: $vscodium_cmd" >&2
    echo "$vscodium_cmd"
}

# Check if VSCodium is available
check_vscodium() {
    VSCODIUM_CMD=$(find_vscodium 2>&1 | tail -1)
    if [[ -z "$VSCODIUM_CMD" ]]; then
        log_error "Failed to find VSCodium executable" >&2
        exit 1
    fi
}

# List fingerprints
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints found. Generate one with --new-fingerprint"
        return
    fi
    
    local count=0
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_id=$(basename "$fp_file" .json)
            local created=$(stat -f %Sm "$fp_file" 2>/dev/null || stat -c %y "$fp_file" 2>/dev/null || echo "unknown")
            echo "  📋 $fp_id (created: $created)"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found"
    else
        log_success "Found $count fingerprint(s)"
    fi
}

# Clean old data
cleanup() {
    log_step "Cleaning up old data..."
    
    # Clean old profiles (older than 7 days)
    if [[ -d "$PROFILES_DIR" ]]; then
        find "$PROFILES_DIR" -type d -name "vscodium-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi
    
    # Clean old fingerprints (older than 30 days)
    if [[ -d "$FINGERPRINTS_DIR" ]]; then
        find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# Generate simple mock fingerprint (no Rust dependency)
generate_mock_fingerprint() {
    local fp_id="$1"
    local fp_path="$2"

    log_step "Generating mock fingerprint: $fp_id" >&2
    
    mkdir -p "$(dirname "$fp_path")"
    
    # Generate a simple JSON fingerprint with VSCodium-specific metadata
    cat > "$fp_path" << EOF
{
  "id": "$fp_id",
  "version": "1.0.0",
  "created_at": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "privacy_level": "$PRIVACY_LEVEL",
  "rotation_enabled": true,
  "editor_type": "vscodium",
  "device_id": "$(openssl rand -hex 16)",
  "hardware_signature": {
    "cpu_signature": "mock-cpu-$(openssl rand -hex 4)",
    "memory_class": "8-16GB",
    "architecture": "$(uname -m)",
    "hardware_uuid_hash": "$(openssl rand -hex 16)",
    "performance_class": "high"
  },
  "system_signature": {
    "os_family": "$(uname -s)",
    "os_version_class": "recent",
    "timezone_class": "$(date +%Z)",
    "locale_class": "en-US"
  },
  "vscodium_signature": {
    "version_class": "1.80-1.90",
    "machine_id_hash": "$(openssl rand -hex 16)",
    "session_id_hash": "$(openssl rand -hex 16)",
    "telemetry_disabled": true,
    "marketplace_override": "open-vsx.org"
  },
  "network_signature": {
    "mac_signature": "$(openssl rand -hex 12)",
    "network_class": "ethernet"
  },
  "privacy_metadata": {
    "anonymization_level": "$PRIVACY_LEVEL",
    "data_minimization_applied": true,
    "tracking_protection_enabled": true,
    "microsoft_telemetry_disabled": true
  },
  "fingerprint_hash": "$(openssl rand -hex 32)"
}
EOF
    
    log_success "Mock fingerprint generated: $fp_id" >&2
}

# Try to use Rust tool, fallback to mock
generate_fingerprint() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="fp_${timestamp}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    # Try Rust tool first
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]] && command -v cargo &> /dev/null; then
        log_step "Attempting to use Rust fingerprint generator..." >&2

        cd "$PROJECT_ROOT"
        # Set environment variable to bypass ethics prompt for automated usage
        export SECURITY_TOOLS_ETHICS_ACCEPTED=true
        # Redirect all output to suppress colored logs, only check exit code
        if cargo run --release -p privacy-fingerprint-generator -- \
            generate \
            --real-system \
            --save-path "$fp_path" \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation \
            --format json >/dev/null 2>&1; then
            log_success "Rust fingerprint generated: $fp_id" >&2
            echo "$fp_id"
            return
        else
            log_warning "Rust tool failed, using mock fingerprint" >&2
        fi
    fi

    # Fallback to mock
    generate_mock_fingerprint "$fp_id" "$fp_path" >&2
    echo "$fp_id"
}

# Create isolated VSCodium profile
create_profile() {
    local fp_id="$1"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    local profile_name="vscodium-fp-${fp_id}"
    local profile_dir="$PROFILES_DIR/$profile_name"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Creating isolated VSCodium profile: $profile_name" >&2
    log_info "Profile directory: $profile_dir" >&2

    # Create directories
    if ! mkdir -p "$user_data_dir/User/globalStorage"; then
        log_error "Failed to create user data directory: $user_data_dir/User/globalStorage" >&2
        exit 1
    fi

    if ! mkdir -p "$extensions_dir"; then
        log_error "Failed to create extensions directory: $extensions_dir" >&2
        exit 1
    fi

    # Copy fingerprint
    if [[ -f "$fp_path" ]]; then
        if ! cp "$fp_path" "$user_data_dir/User/globalStorage/fingerprint.json"; then
            log_error "Failed to copy fingerprint to profile" >&2
            exit 1
        fi
        log_info "Fingerprint installed: $user_data_dir/User/globalStorage/fingerprint.json" >&2
    else
        log_warning "Fingerprint file not found: $fp_path" >&2
    fi
    
    # Create VSCodium settings with privacy-focused configuration
    cat > "$user_data_dir/User/settings.json" << EOF
{
  "security.workspace.trust.enabled": false,
  "telemetry.telemetryLevel": "off",
  "update.mode": "none",
  "extensions.autoUpdate": false,
  "workbench.enableExperiments": false,
  "workbench.startupEditor": "none",
  "window.restoreWindows": "none",
  "privacy.fingerprintIsolation": true,
  "privacy.profileName": "$profile_name",
  "privacy.fingerprintId": "$fp_id",
  "privacy.fingerprintPath": "$user_data_dir/User/globalStorage/fingerprint.json",
  "privacy.privacyLevel": "$PRIVACY_LEVEL",
  "augment.fingerprintOverride": "$user_data_dir/User/globalStorage/fingerprint.json",
  "extensions.gallery.serviceUrl": "https://open-vsx.org/vscode/gallery",
  "extensions.gallery.itemUrl": "https://open-vsx.org/vscode/item",
  "extensions.gallery.searchUrl": "https://open-vsx.org/vscode/search",
  "extensions.gallery.resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}",
  "extensions.gallery.cacheUrl": "https://open-vsx.org/vscode/cache",
  "extensions.gallery.controlUrl": "https://open-vsx.org/vscode/control",
  "extensions.gallery.nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions",
  "editor.suggestSelection": "first",
  "vsintellicode.modify.editor.suggestSelection": "automaticallyOverrodeDefaultValue"
}
EOF
    
    # Create product.json override for VSCodium to use Open VSX Registry
    cat > "$user_data_dir/product.json" << EOF
{
  "nameShort": "VSCodium",
  "nameLong": "VSCodium",
  "applicationName": "vscodium",
  "dataFolderName": ".vscodium",
  "win32MutexName": "vscodium",
  "licenseName": "MIT",
  "licenseUrl": "https://github.com/VSCodium/vscodium/blob/master/LICENSE",
  "serverDataFolderName": ".vscodium-server",
  "serverApplicationName": "vscodium-server",
  "extensionsGallery": {
    "serviceUrl": "https://open-vsx.org/vscode/gallery",
    "itemUrl": "https://open-vsx.org/vscode/item",
    "searchUrl": "https://open-vsx.org/vscode/search",
    "resourceUrlTemplate": "https://open-vsx.org/vscode/unpkg/{publisher}/{name}/{version}/{path}",
    "cacheUrl": "https://open-vsx.org/vscode/cache",
    "controlUrl": "https://open-vsx.org/vscode/control",
    "nlsBaseUrl": "https://www.bing.com/api/v7.0/suggestions"
  },
  "linkProtectionTrustedDomains": [
    "https://open-vsx.org",
    "https://vscodium.com"
  ],
  "documentationUrl": "https://go.microsoft.com/fwlink/?LinkID=533484#vscodium",
  "requestFeatureUrl": "https://github.com/VSCodium/vscodium/issues",
  "reportIssueUrl": "https://github.com/VSCodium/vscodium/issues",
  "tipsAndTricksUrl": "https://go.microsoft.com/fwlink/?linkid=852118",
  "twitterUrl": "https://twitter.com/code",
  "privacyStatementUrl": "https://vscodium.com/",
  "telemetryOptOutUrl": "https://vscodium.com/"
}
EOF
    
    log_success "Profile created: $profile_dir" >&2
    echo "$profile_dir"
}

# Launch VSCodium
launch_vscodium() {
    local profile_dir="$1"
    local user_data_dir="$profile_dir/user-data"
    local extensions_dir="$profile_dir/extensions"

    log_step "Launching VSCodium with isolated fingerprint..."

    # Handle Flatpak command differently
    if [[ "$VSCODIUM_CMD" == "flatpak run com.vscodium.codium" ]]; then
        local cmd=(
            flatpak run com.vscodium.codium
            "--user-data-dir=$user_data_dir"
            "--extensions-dir=$extensions_dir"
            "--disable-telemetry"
            "--disable-updates"
            "--disable-crash-reporter"
        )
    else
        local cmd=(
            "$VSCODIUM_CMD"
            "--user-data-dir=$user_data_dir"
            "--extensions-dir=$extensions_dir"
            "--disable-telemetry"
            "--disable-updates"
            "--disable-crash-reporter"
        )
    fi

    if [[ -d "$WORKSPACE_PATH" ]]; then
        cmd+=("$WORKSPACE_PATH")
    fi

    log_info "Starting VSCodium..."
    log_info "Profile: $profile_dir"
    log_info "Workspace: $WORKSPACE_PATH"
    log_info "Privacy Level: $PRIVACY_LEVEL"
    log_info "Command: ${cmd[*]}"
    echo ""

    # Try to launch VSCodium and capture any error output
    if "${cmd[@]}" 2>/tmp/vscodium-error.log; then
        log_success "VSCodium launched successfully"
        log_info "Fingerprint details: $user_data_dir/User/globalStorage/fingerprint.json"
        log_info "Profile directory: $profile_dir"
        log_info "Extensions marketplace: Open VSX Registry (https://open-vsx.org)"
    else
        log_error "VSCodium failed to start"
        if [[ -f /tmp/vscodium-error.log ]]; then
            log_error "Error output:"
            cat /tmp/vscodium-error.log
        fi
        exit 1
    fi
}

# Main function
main() {
    log_info "Simple VSCodium Fingerprint Runner"
    echo ""
    
    if [[ "$LIST_MODE" == "true" ]]; then
        list_fingerprints
        exit 0
    fi
    
    if [[ "$CLEAN_MODE" == "true" ]]; then
        cleanup
        exit 0
    fi
    
    check_vscodium
    
    local fp_id=""
    if [[ "$GENERATE_NEW" == "true" ]]; then
        # Capture fingerprint ID properly, stripping any ANSI codes
        fp_id=$(generate_fingerprint | tail -1 | sed 's/\x1b\[[0-9;]*m//g' | tr -d '\n')

        # Validate the fingerprint ID format
        if [[ ! "$fp_id" =~ ^fp_[0-9]{8}_[0-9]{6}$ ]]; then
            log_error "Invalid fingerprint ID generated: '$fp_id'"
            log_error "Falling back to timestamp-based ID"
            fp_id="fp_$(date +%Y%m%d_%H%M%S)"
        fi
    elif [[ -n "$FINGERPRINT_ID" ]]; then
        fp_id="$FINGERPRINT_ID"
        if [[ ! -f "$FINGERPRINTS_DIR/${fp_id}.json" ]]; then
            log_error "Fingerprint not found: $fp_id"
            list_fingerprints
            exit 1
        fi
    else
        log_error "No fingerprint specified. Use --new-fingerprint or --fingerprint-id"
        show_help
        exit 1
    fi

    log_info "Using fingerprint: $fp_id"

    # Create profile and capture directory path properly
    local profile_dir
    profile_dir=$(create_profile "$fp_id" 2>&1 | tail -1)

    # Validate profile directory
    if [[ ! -d "$profile_dir" ]]; then
        log_error "Failed to create profile directory: $profile_dir"
        exit 1
    fi

    launch_vscodium "$profile_dir"
}

main "$@"
