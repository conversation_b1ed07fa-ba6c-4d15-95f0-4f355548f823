#!/usr/bin/env bash

# Nix Firefox Runner with Dynamic Fingerprint Protection
# Runs Firefox with privacy-focused fingerprint protection on each execution

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FINGERPRINTS_DIR="$PROJECT_ROOT/.fingerprints"
PROFILES_DIR="/tmp/firefox-fingerprint-profiles"
FIREFOX_CMD=""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_step() { echo -e "${PURPLE}🔧 $1${NC}"; }

show_help() {
    cat << EOF
Nix Firefox Fingerprint Runner

USAGE:
    $0 [OPTIONS] [URL]

OPTIONS:
    --new-fingerprint, -n    Generate new fingerprint and run Firefox
    --fingerprint-id ID      Use specific fingerprint by ID
    --privacy-level LEVEL    Privacy level (low, medium, high, maximum) [default: maximum]
    --list, -l               List available fingerprints
    --clean, -c              Clean up old profiles and fingerprints
    --private, -p            Run in private browsing mode
    --tor-mode               Enable Tor-like privacy settings
    --help, -h               Show this help

EXAMPLES:
    $0 --new-fingerprint                        # New fingerprint, blank page
    $0 --new-fingerprint https://example.com    # New fingerprint, specific URL
    $0 --fingerprint-id fp_20241201_123456      # Use existing fingerprint
    $0 --private --tor-mode https://example.com # Maximum privacy mode
    $0 --list                                   # List available fingerprints

FIREFOX INSTALLATION (NIX):
    This script uses Nix to provide a reproducible Firefox environment.
    
    Install Nix: https://nixos.org/download.html
    
    The script will automatically set up Firefox with privacy extensions:
    - uBlock Origin
    - Privacy Badger
    - Decentraleyes
    - ClearURLs
    - Canvas Blocker

PRIVACY FEATURES:
    🔒 Dynamic user agent rotation
    🔒 Randomized canvas fingerprinting protection
    🔒 WebGL fingerprinting protection
    🔒 Audio fingerprinting protection
    🔒 Screen resolution spoofing
    🔒 Timezone spoofing
    🔒 Language spoofing
    🔒 Cookie and tracking protection

EOF
}

# Parse arguments
GENERATE_NEW=false
FINGERPRINT_ID=""
PRIVACY_LEVEL="maximum"
LIST_MODE=false
CLEAN_MODE=false
PRIVATE_MODE=false
TOR_MODE=false
TARGET_URL=""

while [[ $# -gt 0 ]]; do
    case $1 in
        --new-fingerprint|-n)
            GENERATE_NEW=true
            shift
            ;;
        --fingerprint-id)
            FINGERPRINT_ID="$2"
            shift 2
            ;;
        --privacy-level)
            PRIVACY_LEVEL="$2"
            shift 2
            ;;
        --list|-l)
            LIST_MODE=true
            shift
            ;;
        --clean|-c)
            CLEAN_MODE=true
            shift
            ;;
        --private|-p)
            PRIVATE_MODE=true
            shift
            ;;
        --tor-mode)
            TOR_MODE=true
            PRIVACY_LEVEL="maximum"
            shift
            ;;
        --help|-h)
            show_help
            exit 0
            ;;
        -*)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
        *)
            TARGET_URL="$1"
            shift
            ;;
    esac
done

# Check if Nix is available (optional for this script)
check_nix() {
    # Try to source Nix environment first
    if [[ -f '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh' ]]; then
        source '/nix/var/nix/profiles/default/etc/profile.d/nix-daemon.sh'
    fi

    # Add Nix to PATH if not already there
    if [[ ":$PATH:" != *":/nix/var/nix/profiles/default/bin:"* ]]; then
        export PATH="/nix/var/nix/profiles/default/bin:$PATH"
    fi

    if command -v nix-shell &> /dev/null; then
        log_success "Nix is available - using Nix environment"
        return 0
    else
        log_warning "Nix not available - using system Firefox"
        log_info "For full Nix features, install Nix from: https://nixos.org/download.html"
        return 1
    fi
}

# Create Nix shell configuration for Firefox with privacy extensions
create_nix_shell() {
    local profile_dir="$1"
    local nix_file="$profile_dir/shell.nix"
    
    mkdir -p "$profile_dir"
    
    cat > "$nix_file" << 'EOF'
{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Use minimal Nix environment for profile management
  ] ++ lib.optionals stdenv.isLinux [
    firefox
    tor
    # proxychains only available on Linux
  ];
  
  shellHook = ''
    export FIREFOX_PROFILE_DIR="$PWD/firefox-profile"
    
    # Create Firefox profile directory if it doesn't exist
    mkdir -p "$FIREFOX_PROFILE_DIR"
    
    # Set privacy-focused preferences
    cat > "$FIREFOX_PROFILE_DIR/user.js" << 'PREFS'
// Privacy-focused Firefox configuration
// Disable telemetry
user_pref("toolkit.telemetry.enabled", false);
user_pref("toolkit.telemetry.unified", false);
user_pref("toolkit.telemetry.server", "");
user_pref("datareporting.healthreport.uploadEnabled", false);
user_pref("datareporting.policy.dataSubmissionEnabled", false);

// Disable Firefox studies
user_pref("app.shield.optoutstudies.enabled", false);
user_pref("app.normandy.enabled", false);

// Disable Pocket
user_pref("extensions.pocket.enabled", false);

// Enhanced privacy settings
user_pref("privacy.trackingprotection.enabled", true);
user_pref("privacy.trackingprotection.socialtracking.enabled", true);
user_pref("privacy.trackingprotection.cryptomining.enabled", true);
user_pref("privacy.trackingprotection.fingerprinting.enabled", true);

// Disable WebRTC (prevents IP leaks)
user_pref("media.peerconnection.enabled", false);

// Disable geolocation
user_pref("geo.enabled", false);
user_pref("geo.provider.network.url", "");

// Canvas fingerprinting protection
user_pref("privacy.resistFingerprinting", true);
user_pref("privacy.resistFingerprinting.letterboxing", true);

// WebGL fingerprinting protection
user_pref("webgl.disabled", true);

// Audio fingerprinting protection
user_pref("media.webaudio.enabled", false);

// Battery API (fingerprinting vector)
user_pref("dom.battery.enabled", false);

// Gamepad API (fingerprinting vector)
user_pref("dom.gamepad.enabled", false);

// Sensor APIs (fingerprinting vectors)
user_pref("device.sensors.enabled", false);
user_pref("dom.event.ambient-light.enabled", false);
user_pref("dom.event.device-orientation.enabled", false);

// Network settings
user_pref("network.http.sendRefererHeader", 0);
user_pref("network.http.referer.XOriginPolicy", 2);
user_pref("network.http.referer.XOriginTrimmingPolicy", 2);

// Cookie settings
user_pref("network.cookie.cookieBehavior", 1);
user_pref("network.cookie.lifetimePolicy", 2);

// DNS over HTTPS
user_pref("network.trr.mode", 2);
user_pref("network.trr.uri", "https://1.1.1.1/dns-query");

// Disable prefetching
user_pref("network.dns.disablePrefetch", true);
user_pref("network.prefetch-next", false);
user_pref("network.predictor.enabled", false);

// User agent spoofing preparation
user_pref("general.useragent.override", "PLACEHOLDER_USER_AGENT");

// Timezone spoofing
user_pref("privacy.resistFingerprinting.jsDateTimeUTC", true);

// Language spoofing
user_pref("intl.accept_languages", "PLACEHOLDER_LANGUAGES");

// Screen resolution spoofing (handled by resistFingerprinting)
user_pref("privacy.window.maxInnerWidth", 1000);
user_pref("privacy.window.maxInnerHeight", 1000);

// Disable automatic connections
user_pref("app.update.auto", false);
user_pref("extensions.update.autoUpdateDefault", false);
user_pref("browser.search.update", false);
user_pref("browser.safebrowsing.downloads.remote.enabled", false);

// Disable Firefox Sync
user_pref("services.sync.engine.addons", false);
user_pref("services.sync.engine.bookmarks", false);
user_pref("services.sync.engine.history", false);
user_pref("services.sync.engine.passwords", false);
user_pref("services.sync.engine.prefs", false);
user_pref("services.sync.engine.tabs", false);

// Additional fingerprinting resistance
user_pref("media.eme.enabled", false);
user_pref("media.gmp-widevinecdm.enabled", false);
user_pref("dom.webaudio.enabled", false);
user_pref("javascript.use_us_english_locale", true);
PREFS

    echo "Firefox profile created at: $FIREFOX_PROFILE_DIR"
  '';
}
EOF
    
    echo "$nix_file"
}

# List fingerprints
list_fingerprints() {
    log_step "Available fingerprints:"
    
    if [[ ! -d "$FINGERPRINTS_DIR" ]]; then
        log_warning "No fingerprints found. Generate one with --new-fingerprint"
        return
    fi
    
    local count=0
    for fp_file in "$FINGERPRINTS_DIR"/*.json; do
        if [[ -f "$fp_file" ]]; then
            local fp_id=$(basename "$fp_file" .json)
            local created=$(stat -f %Sm "$fp_file" 2>/dev/null || stat -c %y "$fp_file" 2>/dev/null || echo "unknown")
            echo "  📋 $fp_id (created: $created)"
            ((count++))
        fi
    done
    
    if [[ $count -eq 0 ]]; then
        log_warning "No fingerprints found"
    else
        log_success "Found $count fingerprint(s)"
    fi
}

# Clean old data
cleanup() {
    log_step "Cleaning up old data..."
    
    # Clean old profiles (older than 7 days)
    if [[ -d "$PROFILES_DIR" ]]; then
        find "$PROFILES_DIR" -type d -name "firefox-fp-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    fi
    
    # Clean old fingerprints (older than 30 days)
    if [[ -d "$FINGERPRINTS_DIR" ]]; then
        find "$FINGERPRINTS_DIR" -name "*.json" -mtime +30 -delete 2>/dev/null || true
    fi
    
    log_success "Cleanup completed"
}

# Generate privacy-focused browser fingerprint
generate_browser_fingerprint() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local fp_id="firefox_fp_${timestamp}"
    local fp_path="$FINGERPRINTS_DIR/${fp_id}.json"

    # Try Rust tool first
    if [[ -f "$PROJECT_ROOT/Cargo.toml" ]] && command -v cargo &> /dev/null; then
        log_step "Attempting to use Rust fingerprint generator..." >&2

        cd "$PROJECT_ROOT"
        # Set environment variable to bypass ethics prompt for automated usage
        export SECURITY_TOOLS_ETHICS_ACCEPTED=true
        # Generate browser-specific fingerprint
        if cargo run --release -p privacy-fingerprint-generator -- \
            generate \
            --real-system \
            --save-path "$fp_path" \
            --privacy-level "$PRIVACY_LEVEL" \
            --enable-rotation \
            --format json >/dev/null 2>&1; then
            log_success "Rust fingerprint generated: $fp_id" >&2
            echo "$fp_id"
            return
        else
            log_warning "Rust tool failed, using mock fingerprint" >&2
        fi
    fi

    # Fallback to mock browser fingerprint
    generate_mock_browser_fingerprint "$fp_id" "$fp_path" >&2
    echo "$fp_id"
}

# Generate mock browser fingerprint
generate_mock_browser_fingerprint() {
    local fp_id="$1"
    local fp_path="$2"

    log_step "Generating mock browser fingerprint: $fp_id" >&2
    
    mkdir -p "$(dirname "$fp_path")"

    # Browser-specific user agents for different privacy levels
    local user_agents=()
    case "$PRIVACY_LEVEL" in
        "low")
            user_agents=(
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                "Mozilla/5.0 (X11; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0"
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
            )
            ;;
        "medium")
            user_agents=(
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:90.0) Gecko/20100101 Firefox/90.0"
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0"
            )
            ;;
        "high"|"maximum")
            user_agents=(
                "Mozilla/5.0 (Windows NT 10.0; rv:78.0) Gecko/20100101 Firefox/78.0"
                "Mozilla/5.0 (X11; Linux x86_64; rv:78.0) Gecko/20100101 Firefox/78.0"
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:78.0) Gecko/20100101 Firefox/78.0"
            )
            ;;
    esac

    local languages=("en-US,en;q=0.9" "en-GB,en;q=0.9" "en-CA,en;q=0.9" "en-AU,en;q=0.9")
    local timezones=("America/New_York" "Europe/London" "America/Los_Angeles" "UTC")
    local screen_resolutions=("1920x1080" "1366x768" "1440x900" "1280x720")

    # Select random values
    local selected_ua=${user_agents[$RANDOM % ${#user_agents[@]}]}
    local selected_lang=${languages[$RANDOM % ${#languages[@]}]}
    local selected_tz=${timezones[$RANDOM % ${#timezones[@]}]}
    local selected_res=${screen_resolutions[$RANDOM % ${#screen_resolutions[@]}]}

    cat > "$fp_path" << EOF
{
  "id": "$fp_id",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "privacy_level": "$PRIVACY_LEVEL",
  "browser_type": "firefox",
  "user_agent": "$selected_ua",
  "languages": "$selected_lang",
  "timezone": "$selected_tz",
  "screen_resolution": "$selected_res",
  "canvas_fingerprint_blocked": true,
  "webgl_fingerprint_blocked": true,
  "audio_fingerprint_blocked": true,
  "webrtc_disabled": true,
  "javascript_enabled": true,
  "cookies_enabled": false,
  "do_not_track": true,
  "private_browsing": $PRIVATE_MODE,
  "tor_mode": $TOR_MODE,
  "extensions": [
    "ublock_origin",
    "privacy_badger",
    "decentraleyes",
    "clearurls",
    "canvas_blocker"
  ],
  "dns_over_https": true,
  "fingerprinting_resistance": true
}
EOF
    
    log_success "Mock browser fingerprint generated: $fp_id" >&2
}

# Apply fingerprint to Firefox profile
apply_fingerprint_to_profile() {
    local fp_path="$1"
    local profile_dir="$2"
    
    if [[ ! -f "$fp_path" ]]; then
        log_error "Fingerprint file not found: $fp_path"
        return 1
    fi
    
    log_step "Applying fingerprint to Firefox profile..."
    
    # Read fingerprint data
    local user_agent=$(jq -r '.user_agent // "Mozilla/5.0 (X11; Linux x86_64; rv:78.0) Gecko/20100101 Firefox/78.0"' "$fp_path")
    local languages=$(jq -r '.languages // "en-US,en;q=0.9"' "$fp_path")
    
    # Update user.js file with fingerprint data
    local user_js="$profile_dir/firefox-profile/user.js"
    
    if [[ -f "$user_js" ]]; then
        # Replace placeholder values
        sed -i.bak "s|PLACEHOLDER_USER_AGENT|$user_agent|g" "$user_js"
        sed -i.bak "s|PLACEHOLDER_LANGUAGES|$languages|g" "$user_js"
        rm -f "$user_js.bak"
        
        log_success "Fingerprint applied to profile"
    else
        log_warning "user.js not found, fingerprint not applied"
    fi
}

# Run Firefox with fingerprint
run_firefox() {
    local fp_id="$1"
    local profile_dir="$2"
    local nix_file="$3"
    
    log_step "Starting Firefox with fingerprint: $fp_id"
    
    # Build Firefox command
    local firefox_args=(
        "--profile" "$profile_dir/firefox-profile"
        "--no-remote"
        "--new-instance"
    )
    
    # Add private browsing if enabled
    if [[ "$PRIVATE_MODE" == "true" ]]; then
        firefox_args+=("--private-window")
    fi
    
    # Add target URL if provided
    if [[ -n "$TARGET_URL" ]]; then
        firefox_args+=("$TARGET_URL")
    fi
    
    # Switch to profile directory and run via Nix
    cd "$profile_dir"
    
    if [[ "$TOR_MODE" == "true" ]]; then
        log_info "Starting Firefox in Tor mode..."
        log_warning "Note: This provides Tor-like privacy settings, but doesn't route through Tor network"
        log_info "For true Tor browsing, use Tor Browser Bundle"
    fi
    
    log_info "Firefox starting with privacy fingerprint..."
    log_info "Profile: $profile_dir/firefox-profile"
    
    # Run Firefox with the configured profile
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # On macOS, use the system Firefox
        if [[ -f "/Applications/Firefox.app/Contents/MacOS/firefox" ]]; then
            log_info "Launching Firefox on macOS..."
            exec "/Applications/Firefox.app/Contents/MacOS/firefox" "${firefox_args[@]}"
        elif command -v firefox &> /dev/null; then
            log_info "Launching Firefox using system PATH..."
            exec firefox "${firefox_args[@]}"
        else
            log_error "Firefox not found. Please install Firefox from https://firefox.com"
            exit 1
        fi
    else
        # On Linux, use Nix-provided Firefox
        exec nix-shell --run "firefox ${firefox_args[*]}"
    fi
}

# Main execution logic
main() {
    log_info "Nix Firefox Fingerprint Runner"
    echo
    
    # Handle special modes
    if [[ "$LIST_MODE" == "true" ]]; then
        list_fingerprints
        exit 0
    fi
    
    if [[ "$CLEAN_MODE" == "true" ]]; then
        cleanup
        exit 0
    fi
    
    # Check dependencies (Nix is optional)
    NIX_AVAILABLE=false
    if check_nix; then
        NIX_AVAILABLE=true
    fi
    
    # Determine fingerprint to use
    local fp_id=""
    local fp_path=""
    
    if [[ "$GENERATE_NEW" == "true" ]]; then
        fp_id=$(generate_fingerprint)
        fp_path="$FINGERPRINTS_DIR/${fp_id}.json"
    elif [[ -n "$FINGERPRINT_ID" ]]; then
        fp_id="$FINGERPRINT_ID"
        fp_path="$FINGERPRINTS_DIR/${fp_id}.json"
        
        if [[ ! -f "$fp_path" ]]; then
            log_error "Fingerprint not found: $fp_id"
            log_info "Use --list to see available fingerprints"
            exit 1
        fi
    else
        log_error "No fingerprint specified"
        log_info "Use --new-fingerprint to generate a new one or --fingerprint-id to use an existing one"
        show_help
        exit 1
    fi
    
    # Create temporary profile directory
    local profile_dir="$PROFILES_DIR/firefox-fp-$(date +%s)-$$"
    mkdir -p "$profile_dir"
    
    # Create Nix shell configuration
    local nix_file=$(create_nix_shell "$profile_dir")
    
    # Set up Firefox profile via Nix
    log_step "Setting up Firefox profile with Nix..."
    cd "$profile_dir"
    nix-shell --run "echo 'Firefox profile initialized'"
    
    # Apply fingerprint
    apply_fingerprint_to_profile "$fp_path" "$profile_dir"
    
    # Cleanup function
    cleanup_profile() {
        log_step "Cleaning up temporary profile..."
        rm -rf "$profile_dir"
        log_success "Cleanup completed"
    }
    
    # Set trap for cleanup
    trap cleanup_profile EXIT INT TERM
    
    # Run Firefox
    run_firefox "$fp_id" "$profile_dir" "$nix_file"
}

# Rename function to avoid conflict
generate_fingerprint() {
    generate_browser_fingerprint
}

# Run main function
main "$@"
